import sys
import time
import threading

class LoadingSpinner:
    def __init__(self, text="加载中", speed=0.05):
        self.text = text
        self.speed = speed
        self.spinner_symbols = ["|", "/", "-", "\\"]
        self.spinner_index = 0
        self.loading = True

    def start(self):
        sys.stdout.write(self.text)
        sys.stdout.flush()
        threading.Thread(target=self.animate).start()

    def stop(self):
        self.loading = False
        sys.stdout.write("\r")
        sys.stdout.flush()

    def animate(self):
        while self.loading:
            sys.stdout.write(f"\r{self.text} {self.spinner_symbols[self.spinner_index]}")
            sys.stdout.flush()
            self.spinner_index = (self.spinner_index + 1) % len(self.spinner_symbols)
            time.sleep(self.speed)