import sys
import signal
import re
import threading

from ast import literal_eval

import scapy.libs.six as six
from scapy.config import conf
from scapy.consts import LINUX
from scapy.compat import Tuple, Optional, Any

if six.PY2 or not LINUX or conf.use_pypy:
    conf.contribs['CANSocket'] = {'use-python-can': True}

from scapy.contrib.cansocket import CANSocket, PYTHON_CAN   # noqa: E402
from utils.isotp_scanner import isotp_scan  # noqa: E402

def create_socket(python_can_args, interface, channel,canfd_OPT):

    if PYTHON_CAN:
        if python_can_args:
            interface_string = "CANSocket(bustype=" \
                               "'%s', channel='%s', %s)" % \
                               (interface, channel, python_can_args)
            arg_dict = dict((k, literal_eval(v)) for k, v in
                            (pair.split('=') for pair in
                             re.split(', | |,', python_can_args)))
            sock = CANSocket(bustype=interface, channel=channel,fd=canfd_OPT
                             **arg_dict)
        else:
            interface_string = "CANSocket(bustype=" \
                               "'%s', channel='%s')" % \
                               (interface, channel)
            sock = CANSocket(bustype=interface, channel=channel,fd=canfd_OPT)
    else:
        sock = CANSocket(channel=channel,fd=canfd_OPT)
        interface_string = "\"%s\"" % channel

    return sock, interface_string


def scanner_uds_id(ichannel,canfd_OPT,ext,start,end,timeout):
    extended = False
    piso = False
    verbose = False
    extended_can_id = ext
    sniff_time = timeout
    noise_listen_time = 2
    channel = ichannel
    interface = None
    python_can_args = None


    try:
        sock, interface_string = \
            create_socket(python_can_args, interface, channel,canfd_OPT)

        if verbose:
            print("Start scan (%s - %s)" % (hex(start), hex(end)))

        stop_event = threading.Event()

        def signal_handler(*args):
            # type: (Any) -> None
            print('Interrupting scan!')
            stop_event.set()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        result = isotp_scan(sock,
                            range(start, end + 1),
                            extended_addressing=extended,
                            noise_listen_time=noise_listen_time,
                            sniff_time=float(sniff_time) / 1000,
                            output_format="code" if piso else "text",
                            can_interface=interface_string,
                            extended_can_id=extended_can_id,
                            verbose=verbose,
                            stop_event=stop_event)
        clientID = []
        serverID = []

        output = result.split('\n')
        for each in output:
            if 'Send' in each:
                clientID.append(each.replace(' ','').replace('\n','').split(':')[1])
            elif 'Received' in each:
                serverID.append(each.replace(' ','').replace('\n','').split(':')[1])

        return clientID,serverID

    except Exception as e:
        print("\nSocket couldn't be created. Check your arguments.\n",
              file=sys.stderr)
        print(e, file=sys.stderr)
        sys.exit(1)

    finally:
        if sock is not None and not sock.closed:
            sock.close()
